package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnDO;
import com.pugwoo.branch.database.model.ColumnInfoDTO;
import com.pugwoo.branch.database.service.DatabaseStdWideTableColumnService;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/database_std_wide_table_column")
public class DatabaseStdWideTableColumnController {

    @Autowired
    private DatabaseStdWideTableColumnService databaseStdWideTableColumnService;
    
    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize, Long tableId) {
        PageData<DatabaseStdWideTableColumnDO> pageData = databaseStdWideTableColumnService.getPage(page, pageSize, tableId);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(DatabaseStdWideTableColumnDO databaseStdWideTableColumnDO) {
        WebCheckUtils.assertNotNull(databaseStdWideTableColumnDO, "缺少修改的对象参数");
        // TODO check parameters

        ResultBean<Long> result = databaseStdWideTableColumnService.insertOrUpdate(databaseStdWideTableColumnDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(databaseStdWideTableColumnService.deleteById(id));
    }

    @GetMapping("get_unadded_columns")
    public WebJsonBean<List<ColumnInfoDTO>> getUnaddedColumns(Long tableId) {
        WebCheckUtils.assertNotNull(tableId, "缺少参数tableId");
        List<ColumnInfoDTO> unaddedColumns = databaseStdWideTableColumnService.getUnaddedColumns(tableId);
        return WebJsonBean.ok(unaddedColumns);
    }

    @PostMapping("change_uniq_dim")
    public WebJsonBean<?> changeUniqDim(Long id, Boolean enabled) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(enabled, "缺少参数enabled");
        DatabaseStdWideTableColumnDO columnDO = new DatabaseStdWideTableColumnDO();
        columnDO.setId(id);
        columnDO.setIsUniqDim(enabled);
        ResultBean<Long> result = databaseStdWideTableColumnService.insertOrUpdate(columnDO);
        return result.isSuccess() ? WebJsonBean.ok() : WebJsonBean.of(result);
    }

    @PostMapping("change_enum")
    public WebJsonBean<?> changeEnum(Long id, Boolean enabled) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotNull(enabled, "缺少参数enabled");
        DatabaseStdWideTableColumnDO columnDO = new DatabaseStdWideTableColumnDO();
        columnDO.setId(id);
        columnDO.setIsEnum(enabled);
        ResultBean<Long> result = databaseStdWideTableColumnService.insertOrUpdate(columnDO);
        return result.isSuccess() ? WebJsonBean.ok() : WebJsonBean.of(result);
    }

}
