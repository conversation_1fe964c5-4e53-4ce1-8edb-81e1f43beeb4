
#parse("database/std_table/database_std_wide_table_column_enum.vm")

<style>
</style>

<script type="text/x-template" id="databaseStdWideTableColumn">

    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="ID">
                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <el-form :data="props" class="database-std-wide-table">
                        <el-form-item label="值个数">{{props.row.distinctValueCount}}</el-form-item>
                        <el-form-item label="包含空值">{{props.row.isContainNull ? 'true' : 'false'}}</el-form-item>
                        <el-form-item label="最小值">{{props.row.minValue}}</el-form-item>
                        <el-form-item label="最大值">{{props.row.maxValue}}</el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="名称" width="120"></el-table-column>
            <el-table-column prop="columnName" label="列名" width="120"></el-table-column>
            <el-table-column prop="columnType" label="列类型" width="120"></el-table-column>
            <el-table-column label="最细维度" width="90">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.isUniqDim" active-color="#13ce66" inactive-color="#BEBEBE"
                               @change="changeUniqDim($event, scope.row.id)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="枚举类型" width="400">
                <template slot-scope="scope">
                    <div>
                        <el-switch v-model="scope.row.isEnum" active-color="#13ce66" inactive-color="#BEBEBE"
                                   @change="changeEnum($event, scope.row.id)"></el-switch>
                        <div v-if="scope.row.isEnum" style="margin-top: 5px;">
                            <div v-if="scope.row.enumValues && scope.row.enumValues.length > 0">
                                <el-tag v-for="(enumItem, index) in scope.row.enumValues" :key="enumItem.id"
                                        closable @close="deleteEnumValue(scope.row.id, enumItem.id, index)"
                                        style="margin-right: 5px; margin-bottom: 3px;">
                                    <span @dblclick="editEnumValue(scope.row.id, enumItem, index)"
                                          :title="'双击编辑: ' + enumItem.enumValue">
                                        {{enumItem.enumValue}}
                                    </span>
                                </el-tag>
                            </div>
                            <el-button size="mini" type="primary" @click="addEnumValue(scope.row.id)"
                                       style="margin-top: 3px;">
                                <i class="el-icon-plus"></i> 添加枚举值
                            </el-button>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="note" label="说明"></el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>

        <!-- 未添加的列区域 -->
        <div style="margin-top: 60px;">
            <el-divider content-position="left">
                <span style="font-size: 16px; font-weight: bold;">数据库表中未添加的列</span>
            </el-divider>

            <el-button type="primary" @click="getUnaddedColumns" style="margin-bottom: 10px;">
                <i class="el-icon-refresh"></i> 刷新未添加列
            </el-button>

            <el-table :data="unaddedColumnsData" border stripe v-loading.body="unaddedColumnsLoading" v-if="unaddedColumnsData.length > 0">
                <el-table-column prop="columnName" label="列名" width="200"></el-table-column>
                <el-table-column prop="columnComment" label="列注释"></el-table-column>
                <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                        <el-button type="success" size="small" @click="quickAddColumn(scope.row)">添加</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-empty v-if="unaddedColumnsData.length === 0 && !unaddedColumnsLoading" description="暂无未添加的列"></el-empty>
        </div>

        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="addEditForm.name" placeholder="列名称"></el-input>
                </el-form-item>
                <el-form-item label="列名" prop="columnName">
                    <el-input v-model="addEditForm.columnName" placeholder="表中的列名"></el-input>
                </el-form-item>
                <el-form-item label="列类型" prop="columnType">
                    <el-input v-model="addEditForm.columnType" placeholder="列的数据类型"></el-input>
                </el-form-item>
                <el-form-item label="说明" prop="note">
                    <el-input v-model="addEditForm.note" placeholder="列说明"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>

        <!-- 枚举值编辑对话框 -->
        <el-dialog :title="enumDialogTitle" :visible.sync="showEnumDialog" width="400px" :close-on-click-modal="false" :append-to-body='true'>
            <el-form :model="enumEditForm" label-position="right" label-width="100px" ref="enumEditForm">
                <el-form-item label="枚举值" prop="enumValue">
                    <el-input v-model="enumEditForm.enumValue" placeholder="请输入枚举值"></el-input>
                </el-form-item>
                <el-form-item label="说明" prop="note">
                    <el-input v-model="enumEditForm.note" placeholder="枚举值说明"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="showEnumDialog = false">取消</el-button>
                <el-button type="primary" @click="doSaveEnumValue">确定</el-button>
            </div>
        </el-dialog>

    </div>

</script>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var defaultEnumEditForm = {}
    Vue.component('database-std-wide-table-column', {
        template: '#databaseStdWideTableColumn',
        data: function () {
            return {
                queryForm: Utils.copy(defaultQueryForm),
                addEditForm: Utils.copy(defaultAddForm),
                enumEditForm: Utils.copy(defaultEnumEditForm),
                rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
                total: 0, tableData: [], tableLoading: false,
                showDialog: false, dialogTitle: '',
                showEnumDialog: false, enumDialogTitle: '',
                unaddedColumnsData: [], unaddedColumnsLoading: false,
                currentColumnId: null, // 当前编辑枚举值的列ID
                currentEnumIndex: null // 当前编辑的枚举值索引
            }
        },
        props: {
            tableId: Number /*表ID参数*/
        },
        created: function() {
            this.getData()
            this.getUnaddedColumns()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                var params = Utils.copy(this.queryForm)
                if (this.tableId) {
                    params.tableId = this.tableId
                }
                Resource.get("${_contextPath_}/database_std_wide_table_column/get_page", params, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    // 为每个开启枚举的列加载枚举值
                    that.loadEnumValuesForColumns()
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_std_wide_table_column/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                        that.getUnaddedColumns() // 刷新未添加列列表
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增标准宽表列' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
                if (isAdd && this.tableId) {
                    this.addEditForm.tableId = this.tableId
                }
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_std_wide_table_column/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                        that.getUnaddedColumns() // 刷新未添加列列表
                    })
                })
            },
            getUnaddedColumns: function() {
                var that = this
                if (!this.tableId) {
                    return
                }
                that.unaddedColumnsLoading = true
                Resource.get("${_contextPath_}/database_std_wide_table_column/get_unadded_columns", {tableId: this.tableId}, function(resp){
                    that.unaddedColumnsData = resp.data || []
                    that.unaddedColumnsLoading = false
                })
            },
            quickAddColumn: function(columnInfo) {
                var that = this
                Message.confirm("确定要添加列 '" + columnInfo.columnName + "' 吗？", function(){
                    var newColumn = {
                        tableId: that.tableId,
                        name: columnInfo.columnComment || columnInfo.columnName, // 优先使用注释作为名称
                        columnName: columnInfo.columnName,
                        columnType: '', // 这里可以根据需要设置默认值
                        note: columnInfo.columnComment || ''
                    }
                    Resource.post("${_contextPath_}/database_std_wide_table_column/add_or_update", newColumn, function(resp){
                        Message.success("列添加成功")
                        that.getData() // 刷新已添加列列表
                        that.getUnaddedColumns() // 刷新未添加列列表
                    })
                })
            },
            changeUniqDim: function($event, id) {
                var that = this
                Resource.post("${_contextPath_}/database_std_wide_table_column/change_uniq_dim", {
                    id: id, enabled: $event
                }, function(resp) {
                    Message.success("修改成功")
                    that.getData()
                })
            },
            changeEnum: function($event, id) {
                var that = this
                Resource.post("${_contextPath_}/database_std_wide_table_column/change_enum", {
                    id: id, enabled: $event
                }, function(resp) {
                    Message.success("枚举类型修改成功")
                    that.getData()
                })
            },
            loadEnumValuesForColumns: function() {
                var that = this
                this.tableData.forEach(function(row) {
                    if (row.isEnum) {
                        that.loadEnumValues(row.id, function(enumValues) {
                            that.$set(row, 'enumValues', enumValues)
                        })
                    }
                })
            },
            loadEnumValues: function(columnId, callback) {
                Resource.get("${_contextPath_}/database_std_wide_table_column_enum/get_by_column_id", {
                    columnId: columnId
                }, function(resp) {
                    if (callback) {
                        callback(resp.data || [])
                    }
                })
            },
            addEnumValue: function(columnId) {
                this.currentColumnId = columnId
                this.currentEnumIndex = null
                this.enumEditForm = Utils.copy(defaultEnumEditForm)
                this.enumEditForm.columnId = columnId
                this.enumDialogTitle = '新增枚举值'
                this.showEnumDialog = true
            },
            editEnumValue: function(columnId, enumItem, index) {
                this.currentColumnId = columnId
                this.currentEnumIndex = index
                this.enumEditForm = Utils.copy(enumItem)
                this.enumDialogTitle = '编辑枚举值'
                this.showEnumDialog = true
            },
            doSaveEnumValue: function() {
                var that = this
                Resource.post("${_contextPath_}/database_std_wide_table_column_enum/add_or_update", this.enumEditForm, function(resp) {
                    Message.success("枚举值保存成功")
                    that.showEnumDialog = false
                    // 重新加载该列的枚举值
                    that.loadEnumValues(that.currentColumnId, function(enumValues) {
                        var row = that.tableData.find(r => r.id === that.currentColumnId)
                        if (row) {
                            that.$set(row, 'enumValues', enumValues)
                        }
                    })
                })
            },
            deleteEnumValue: function(columnId, enumId, index) {
                var that = this
                Message.confirm("确定要删除这个枚举值吗？", function() {
                    Resource.post("${_contextPath_}/database_std_wide_table_column_enum/delete", {
                        id: enumId
                    }, function(resp) {
                        Message.success("枚举值删除成功")
                        // 重新加载该列的枚举值
                        that.loadEnumValues(columnId, function(enumValues) {
                            var row = that.tableData.find(r => r.id === columnId)
                            if (row) {
                                that.$set(row, 'enumValues', enumValues)
                            }
                        })
                    })
                })
            },
        }
    })
</script>