
#parse("database/std_table/database_std_wide_table_column_enum.vm")

<style>
</style>

<script type="text/x-template" id="databaseStdWideTableColumn">

    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="ID">
                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <el-form :data="props" class="database-std-wide-table">
                        <el-form-item label="值个数">{{props.row.distinctValueCount}}</el-form-item>
                        <el-form-item label="包含空值">{{props.row.isContainNull ? 'true' : 'false'}}</el-form-item>
                        <el-form-item label="最小值">{{props.row.minValue}}</el-form-item>
                        <el-form-item label="最大值">{{props.row.maxValue}}</el-form-item>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="名称" width="120"></el-table-column>
            <el-table-column prop="columnName" label="列名" width="120"></el-table-column>
            <el-table-column prop="columnType" label="列类型" width="120"></el-table-column>
            <el-table-column label="最细维度" width="100">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.isUniqDim" active-color="#13ce66" inactive-color="#BEBEBE"
                               @change="changeUniqDim($event, props.row.id)"></el-switch>
                    <span v-text="scope.row.isUniqDim ? 'true' : 'false'"></span>
                </template>
            </el-table-column>
            <el-table-column label="枚举类型" width="300">
                <template slot-scope="scope">
                    <span v-text="scope.row.isEnum ? 'true' : 'false'"></span>
                </template>
            </el-table-column>
            <el-table-column prop="note" label="说明"></el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>

        <!-- 未添加的列区域 -->
        <div style="margin-top: 60px;">
            <el-divider content-position="left">
                <span style="font-size: 16px; font-weight: bold;">数据库表中未添加的列</span>
            </el-divider>

            <el-button type="primary" @click="getUnaddedColumns" style="margin-bottom: 10px;">
                <i class="el-icon-refresh"></i> 刷新未添加列
            </el-button>

            <el-table :data="unaddedColumnsData" border stripe v-loading.body="unaddedColumnsLoading" v-if="unaddedColumnsData.length > 0">
                <el-table-column prop="columnName" label="列名" width="200"></el-table-column>
                <el-table-column prop="columnComment" label="列注释"></el-table-column>
                <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                        <el-button type="success" size="small" @click="quickAddColumn(scope.row)">添加</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-empty v-if="unaddedColumnsData.length === 0 && !unaddedColumnsLoading" description="暂无未添加的列"></el-empty>
        </div>

        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="addEditForm.name" placeholder="列名称"></el-input>
                </el-form-item>
                <el-form-item label="列名" prop="columnName">
                    <el-input v-model="addEditForm.columnName" placeholder="表中的列名"></el-input>
                </el-form-item>
                <el-form-item label="列类型" prop="columnType">
                    <el-input v-model="addEditForm.columnType" placeholder="列的数据类型"></el-input>
                </el-form-item>
                <el-form-item label="说明" prop="note">
                    <el-input v-model="addEditForm.note" placeholder="列说明"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>

    </div>

</script>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    Vue.component('database-std-wide-table-column', {
        template: '#databaseStdWideTableColumn',
        data: function () {
            return {
                queryForm: Utils.copy(defaultQueryForm),
                addEditForm: Utils.copy(defaultAddForm),
                rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
                total: 0, tableData: [], tableLoading: false,
                showDialog: false, dialogTitle: '',
                unaddedColumnsData: [], unaddedColumnsLoading: false
            }
        },
        props: {
            tableId: Number /*表ID参数*/
        },
        created: function() {
            this.getData()
            this.getUnaddedColumns()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                var params = Utils.copy(this.queryForm)
                if (this.tableId) {
                    params.tableId = this.tableId
                }
                Resource.get("${_contextPath_}/database_std_wide_table_column/get_page", params, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_std_wide_table_column/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                        that.getUnaddedColumns() // 刷新未添加列列表
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增标准宽表列' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
                if (isAdd && this.tableId) {
                    this.addEditForm.tableId = this.tableId
                }
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_std_wide_table_column/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                        that.getUnaddedColumns() // 刷新未添加列列表
                    })
                })
            },
            getUnaddedColumns: function() {
                var that = this
                if (!this.tableId) {
                    return
                }
                that.unaddedColumnsLoading = true
                Resource.get("${_contextPath_}/database_std_wide_table_column/get_unadded_columns", {tableId: this.tableId}, function(resp){
                    that.unaddedColumnsData = resp.data || []
                    that.unaddedColumnsLoading = false
                })
            },
            quickAddColumn: function(columnInfo) {
                var that = this
                Message.confirm("确定要添加列 '" + columnInfo.columnName + "' 吗？", function(){
                    var newColumn = {
                        tableId: that.tableId,
                        name: columnInfo.columnComment || columnInfo.columnName, // 优先使用注释作为名称
                        columnName: columnInfo.columnName,
                        columnType: '', // 这里可以根据需要设置默认值
                        note: columnInfo.columnComment || ''
                    }
                    Resource.post("${_contextPath_}/database_std_wide_table_column/add_or_update", newColumn, function(resp){
                        Message.success("列添加成功")
                        that.getData() // 刷新已添加列列表
                        that.getUnaddedColumns() // 刷新未添加列列表
                    })
                })
            },
            changeUniqDim: function($event, id) {
                Resource.post("${_contextPath_}/database_std_wide_table_column/change_uniq_dim", {
                    id: id, enabled: $event
                }, function(resp) {
                    Message.success("修改成功")
                    that.getData()
                })
            },
        }
    })
</script>